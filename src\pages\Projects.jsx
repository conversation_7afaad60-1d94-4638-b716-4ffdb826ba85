import React from 'react'

const Projects = () => {
  const projects = [
    {
      title: "E-Commerce Platform",
      description: "A full-stack e-commerce solution with React frontend and Node.js backend. Features include user authentication, payment integration, and admin dashboard.",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      liveLink: "#",
      githubLink: "#"
    },
    {
      title: "Task Management App",
      description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
      technologies: ["Vue.js", "Express.js", "Socket.io", "PostgreSQL"],
      liveLink: "#",
      githubLink: "#"
    },
    {
      title: "Weather Dashboard",
      description: "A responsive weather application that provides current weather data and forecasts using external APIs with beautiful data visualizations.",
      technologies: ["JavaScript", "Chart.js", "Weather API", "CSS3"],
      liveLink: "#",
      githubLink: "#"
    }
  ]

  return (
    <section className="section" id="projects">
      <div className="container">
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h2>Featured Projects</h2>
          <p>Some of my recent work</p>
        </div>
        
        <div className="grid-3">
          {projects.map((project, index) => (
            <div key={index} className="glass-card">
              <div style={{ 
                height: '200px', 
                background: 'linear-gradient(135deg, var(--accent-primary), var(--accent-secondary))',
                borderRadius: '12px',
                marginBottom: '1.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '3rem'
              }}>
                💻
              </div>
              
              <h3 style={{ color: 'var(--text-accent)', marginBottom: '1rem' }}>
                {project.title}
              </h3>
              
              <p style={{ marginBottom: '1.5rem', fontSize: '0.95rem' }}>
                {project.description}
              </p>
              
              <div style={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: '0.5rem',
                marginBottom: '1.5rem'
              }}>
                {project.technologies.map((tech, techIndex) => (
                  <span 
                    key={techIndex}
                    style={{
                      background: 'var(--accent-glow)',
                      borderRadius: '15px',
                      padding: '0.25rem 0.75rem',
                      fontSize: '0.8rem',
                      color: 'var(--text-primary)'
                    }}
                  >
                    {tech}
                  </span>
                ))}
              </div>
              
              <div style={{ display: 'flex', gap: '1rem' }}>
                <a 
                  href={project.liveLink} 
                  className="btn btn-primary"
                  style={{ flex: 1, textAlign: 'center', fontSize: '0.9rem', padding: '0.75rem' }}
                >
                  Live Demo
                </a>
                <a 
                  href={project.githubLink} 
                  className="btn btn-glass"
                  style={{ flex: 1, textAlign: 'center', fontSize: '0.9rem', padding: '0.75rem' }}
                >
                  GitHub
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Projects
