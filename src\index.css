/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* ===============================
   ROOT VARIABLES - COLOR THEMES
   =============================== */

/* Theme 1: Deep Midnight Blue (Recommended) */
:root {
  /* Background Colors */
  --bg-primary: #0B1426;
  --bg-secondary: #0F1B2E;
  --bg-tertiary: #1E3A5F;
  
  /* Glass Effect Colors */
  --glass-bg: rgba(30, 58, 95, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: rgba(0, 0, 0, 0.3);
  
  /* Text Colors */
  --text-primary: #F7FAFC;
  --text-secondary: #CBD5E0;
  --text-accent: #63B3ED;
  
  /* Accent Colors */
  --accent-primary: #2C5282;
  --accent-secondary: #3182CE;
  --accent-glow: rgba(99, 179, 237, 0.3);
}

/* ===============================
   GLOBAL STYLES
   =============================== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Poppins', sans-serif;
  background-image: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
  background-size: 400% 400%, 300% 300%, 500% 500%;
  background-position: 0% 0%, 100% 100%, 50% 50%;
  background-attachment: fixed;
  background-repeat: no-repeat;
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* ===============================
   TYPOGRAPHY
   =============================== */

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-accent) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 30px var(--accent-glow);
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  color: var(--text-primary);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  color: var(--text-secondary);
}

p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* ===============================
   GLASSMORPHISM COMPONENTS
   =============================== */

.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px var(--glass-shadow),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.glass:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 16px 64px var(--glass-shadow),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 0 1px var(--glass-border);
}

.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 8px 32px var(--glass-shadow),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent
  );
  transition: left 0.6s;
}

.glass-card:hover::before {
  left: 100%;
}

.glass-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 80px var(--glass-shadow),
    inset 0 2px 0 rgba(255, 255, 255, 0.2),
    0 0 0 2px var(--accent-glow);
}

/* ===============================
   NAVIGATION
   =============================== */

.navbar {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 50px;
  box-shadow: 0 8px 32px var(--glass-shadow);
}

.navbar ul {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.navbar a {
  text-decoration: none;
  color: var(--text-secondary);
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 25px;
}

.navbar a:hover,
.navbar a.active {
  color: var(--text-accent);
  background: var(--accent-glow);
  transform: scale(1.05);
}

/* ===============================
   BUTTONS
   =============================== */

.btn {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  color: var(--text-primary);
  box-shadow: 
    0 8px 25px var(--accent-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 15px 35px var(--accent-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-glass {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  box-shadow: 0 8px 32px var(--glass-shadow);
}

.btn-glass:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 15px 35px var(--glass-shadow);
}

/* ===============================
   LAYOUT UTILITIES
   =============================== */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  min-height: 100vh;
  padding: 8rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  align-items: center;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

/* ===============================
   ANIMATIONS
   =============================== */

@keyframes gradientFlow {
  0%, 100% { 
    background-position: 0% 0%, 100% 100%, 50% 50%;
    opacity: 1;
  }
  25% { 
    background-position: 100% 0%, 0% 100%, 25% 75%;
    opacity: 0.9;
  }
  50% { 
    background-position: 100% 100%, 0% 0%, 75% 25%;
    opacity: 0.8;
  }
  75% { 
    background-position: 0% 100%, 100% 0%, 50% 50%;
    opacity: 0.9;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 0 20px var(--accent-glow); 
  }
  50% { 
    box-shadow: 0 0 40px var(--accent-glow), 0 0 60px var(--accent-glow); 
  }
}

.glow-animation {
  animation: glow 3s ease-in-out infinite;
}

/* ===============================
   RESPONSIVE DESIGN
   =============================== */

@media (max-width: 768px) {
  .navbar {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    transform: none;
    padding: 0.75rem 1rem;
  }
  
  .navbar ul {
    gap: 1rem;
    justify-content: space-between;
  }
  
  .navbar a {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .section {
    padding: 6rem 0;
  }
  
  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .glass-card {
    padding: 1.5rem;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .navbar ul {
    gap: 0.5rem;
  }
  
  .navbar a {
    font-size: 0.8rem;
    padding: 0.25rem;
  }
  
  .glass-card {
    padding: 1rem;
  }
  
  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* ===============================
   TEXT EFFECTS
   =============================== */

.text-outline {
  color: transparent;
  -webkit-text-stroke: 2px var(--text-primary);
  font-weight: 700;
  text-shadow:
    0 0 20px var(--accent-glow),
    0 0 40px var(--accent-glow);
}

.text-outline:hover {
  -webkit-text-stroke: 2px var(--text-accent);
  text-shadow:
    0 0 30px var(--accent-glow),
    0 0 60px var(--accent-glow);
  transition: all 0.3s ease;
}

/* ===============================
   ACCESSIBILITY
   =============================== */

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.navbar a:focus,
.btn:focus {
  outline: 2px solid var(--accent-secondary);
  outline-offset: 2px;
}

@media (prefers-contrast: high) {
  :root {
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.3);
    --text-secondary: #FFFFFF;
  }
}