import React from 'react'

const Skills = () => {
  const skillCategories = [
    {
      title: "Frontend",
      skills: ["React", "Vue.js", "JavaScript", "TypeScript", "HTML5", "CSS3", "Tailwind CSS"]
    },
    {
      title: "Backend", 
      skills: ["Node.js", "Python", "Express.js", "Django", "REST APIs", "GraphQL"]
    },
    {
      title: "Database",
      skills: ["MongoDB", "PostgreSQL", "MySQL", "Redis", "Firebase"]
    },
    {
      title: "Tools & Others",
      skills: ["Git", "Docker", "AWS", "Figma", "Webpack", "Vite"]
    }
  ]

  return (
    <section className="section" id="skills">
      <div className="container">
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h2>Skills & Technologies</h2>
          <p>Technologies I work with</p>
        </div>
        
        <div className="grid-2">
          {skillCategories.map((category, index) => (
            <div key={index} className="glass-card">
              <h3 style={{ color: 'var(--text-accent)', marginBottom: '1.5rem' }}>
                {category.title}
              </h3>
              <div style={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: '0.75rem' 
              }}>
                {category.skills.map((skill, skillIndex) => (
                  <span 
                    key={skillIndex}
                    style={{
                      background: 'var(--glass-bg)',
                      border: '1px solid var(--glass-border)',
                      borderRadius: '25px',
                      padding: '0.5rem 1rem',
                      fontSize: '0.9rem',
                      color: 'var(--text-secondary)',
                      backdropFilter: 'blur(10px)',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.background = 'var(--accent-glow)'
                      e.target.style.color = 'var(--text-primary)'
                      e.target.style.transform = 'scale(1.05)'
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.background = 'var(--glass-bg)'
                      e.target.style.color = 'var(--text-secondary)'
                      e.target.style.transform = 'scale(1)'
                    }}
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Skills
