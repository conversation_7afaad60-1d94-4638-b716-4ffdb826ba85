import React from 'react'

const Footer = () => {
  return (
    <footer style={{ 
      background: 'var(--bg-secondary)', 
      padding: '3rem 0',
      borderTop: '1px solid var(--glass-border)'
    }}>
      <div className="container">
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '2rem'
        }}>
          <div>
            <h3 style={{ color: 'var(--text-accent)', marginBottom: '0.5rem' }}>
              Your Name
            </h3>
            <p style={{ margin: 0, fontSize: '0.9rem' }}>
              Full Stack Developer
            </p>
          </div>
          
          <div style={{ display: 'flex', gap: '2rem' }}>
            <a 
              href="https://github.com/yourusername" 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ 
                color: 'var(--text-secondary)', 
                textDecoration: 'none',
                fontSize: '1.5rem',
                transition: 'color 0.3s ease'
              }}
              onMouseEnter={(e) => e.target.style.color = 'var(--text-accent)'}
              onMouseLeave={(e) => e.target.style.color = 'var(--text-secondary)'}
            >
              🐙
            </a>
            <a 
              href="https://linkedin.com/in/yourprofile" 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ 
                color: 'var(--text-secondary)', 
                textDecoration: 'none',
                fontSize: '1.5rem',
                transition: 'color 0.3s ease'
              }}
              onMouseEnter={(e) => e.target.style.color = 'var(--text-accent)'}
              onMouseLeave={(e) => e.target.style.color = 'var(--text-secondary)'}
            >
              💼
            </a>
            <a 
              href="mailto:<EMAIL>"
              style={{ 
                color: 'var(--text-secondary)', 
                textDecoration: 'none',
                fontSize: '1.5rem',
                transition: 'color 0.3s ease'
              }}
              onMouseEnter={(e) => e.target.style.color = 'var(--text-accent)'}
              onMouseLeave={(e) => e.target.style.color = 'var(--text-secondary)'}
            >
              📧
            </a>
          </div>
        </div>
        
        <div style={{ 
          textAlign: 'center', 
          marginTop: '2rem', 
          paddingTop: '2rem',
          borderTop: '1px solid var(--glass-border)'
        }}>
          <p style={{ margin: 0, fontSize: '0.9rem', color: 'var(--text-secondary)' }}>
            © 2024 Your Name. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
