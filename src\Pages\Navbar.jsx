import React from "react"

const Navbar = () => {
    const links = [
        {name: "Home", link: "#home"},
        {name: "About", link: "#about"},
        {name: "Skills", link: "#skills"},
        {name: "Projects", link: "#projects"},
        {name: "Contact", link: "#contact"},
    ]
  return (
    <>
        <nav className="navbar">
            <ul>
                {links.map((link, index) => (
                    <li key={index}>
                        <a href={link.link}>{link.name}</a>
                    </li>
                ))}
            </ul>
        </nav>
    </>
  )
}

export default Navbar
