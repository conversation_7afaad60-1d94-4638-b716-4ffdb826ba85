import React from 'react'

const Navbar = () => {
    const links = [
        {name: 'Home', link: '/'},
        {name: 'About', link: '/about'},
        {name: 'Skills',link: '/skills'},
        {name: 'Contact', link: '/contact'},
        {name: 'Projects', link: '/projects'},
    ]
  return (
    <>
        <div className="flex flex-row justify-between">
            <nav className="navbar">
            <ul>
                {links.map(link => (
                    <li>
                        <a href={link.link}>{link.name}</a>
                    </li>
                ))}
            </ul>
        </nav>
        </div>
    </>
  )
}

export default Navbar